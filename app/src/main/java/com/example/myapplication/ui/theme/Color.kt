package com.example.myapplication.ui.theme

import androidx.compose.ui.graphics.Color

// Màu chính của ứng dụng
val MainColor = Color(0xFFF2CEDF)  // Màu background chính
val AppText = Color(0xFFFFFFFF)

// Màu cho các nút
val ButtonPrimary = Color(0xFFD17878)  // Màu nút chính
val ButtonSecondary = Color(0xFFD9D9D9)  // Màu nút phụ/background của các item
val ButtonSuccess = Color(0xFF4CAF50)  // Màu cho trạng thái thành công
val ButtonDanger = Color(0xFFFF5722)  // Màu cho trạng thái cảnh báo/xóa

// Màu cho text và icon
val TextPrimary = Color.Black  // Màu chữ chính
val TextSecondary = Color.Gray
val IconPrimary = Color.Black
val IconSecondary = Color.Black.copy(alpha = 0.5f)

// Màu cho card và background
val CardBackground = Color(0xFFD9D9D9)
val ProgressBarColor = Color(0xFFE48ED4)
val ProgressBarTrackColor = Color.White.copy(alpha = 0.5f)

// Màu cho chat
val ChatUserBubble = Color(0xFFE3F2FD)
val ChatBotBubble = Color(0xFFD1C4E9)
val ChatBotDotColor = Color(0xFF6A1B9A)

// Màu cho recording
val RecordingActive = Color(0xFFE57373)
val RecordingProcessing = Color(0xFF81C784)
val RecordingDefault = Color(0xFFD9D9D9)

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)
val HomeButtonBackground = Color(0xFFD17878)
val HomeButtonText = Color.White
val HomeSubtitleText = Color(0xFFD17878)

// Màu cho VocabInfo
val TabSelected = Color.White
val TabUnselected = Color(0xFFE0BFD6)
val WordItemBackground = Color(0xFFD9D9D9)

// Màu cho TextFieldBackground
val TextFieldBackground = Color(0xFFD9D9D9)